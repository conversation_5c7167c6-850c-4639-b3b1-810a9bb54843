/* ====
 --------- (6.0) responsive styles start ---------
 ==== */

@media only screen and (max-width: 1699.98px) {
  .sponsor {
    .slick-current + .slick-active + .slick-active + .slick-active {
      img {
        opacity: 0.15;
      }
    }

    .slick-current + .slick-active + .slick-active {
      img {
        opacity: 1;
      }
    }
  }
}

@media only screen and (max-width: 1439.98px) {
  .header {
    .nav__menu-item--dropdown {
      .mega-menu {
        min-width: 1296px !important;
        transform: translateY(30px) translateX(-44%);
      }

      &:hover {
        .mega-menu {
          transform: translateY(0px) translateX(-44%);
        }
      }
    }

    .mega-menu__inner {
      gap: 30px;
    }

    .nav__menu-link {
      padding: 41px 18px;
    }
  }

  .header-two {
    .nav__menu-link {
      padding: 16px;
    }
  }

  .testimonial {
    .slick-current + .slick-active {
      opacity: 25%;
    }
  }

  .counter {
    .counter__item {
      &::before {
        content: none;
      }
    }
  }

  .free {
    .thumb {
      &::before {
        transform: rotate(-16deg) translateX(26px) scaleY(1.5);
      }

      &::after {
        transform: rotate(-16deg) translateX(8px) scaleY(1.5);
      }
    }
  }

  .testimonial-two {
    .testimonial-two-pagination {
      top: 87%;
    }
  }

  .news-two {
    .news-two__slider-item {
      .content {
        width: calc(100% - 350px);
      }

      .thumb {
        width: 350px;
      }
    }

    .news-two__content {
      .news-two-pagination {
        top: 40px !important;
      }
    }
  }

  .registration__content {
    padding-left: 0px;

    .authentic {
      .btn {
        gap: 24px;
      }
    }
  }

  .registration-popup {
    .close-registration {
      top: 30px;
      right: 20px;
    }
  }

  .portfolio {
    .img {
      background-size: 410px 100%;
    }
  }
}

@media only screen and (max-width: 1399.98px) {
  // typography
  h1,
  .h1 {
    font-size: 56px;
    line-height: 70px;
  }

  // header
  .header {
    .nav__logo {
      column-gap: 40px;
    }
    .nav__menu-link {
      padding: 31px 12px;
    }

    .nav__menu-item--dropdown {
      .mega-menu {
        min-width: 1110px !important;
        transform: translateY(30px) translateX(-41%);
      }

      &:hover {
        .mega-menu {
          transform: translateY(0px) translateX(-41%);
        }
      }
    }

    .mega-menu__inner {
      gap: 30px;
    }

    .mega-menu__single--alt {
      max-width: 400px;
      width: 260px;
      width: 100%;

      .rangu {
        height: 300px;
        .img {
          background-size: 100vw 100%;
        }

        .rangu-slider {
          &::-webkit-slider-thumb {
            height: 300px !important;
          }

          &::-moz-range-thumb {
            height: 300px !important;
          }
        }
      }
    }

    .mega-menu__single-item {
      a {
        img {
          max-width: 70px;
        }

        span {
          font-size: 16px;
        }
      }
    }
  }

  .header-two {
    .nav__menu-link {
      padding: 16px;
    }
  }

  // about section
  .about-section {
    .about-section__content {
      padding-left: 0px;
    }

    .about-section__thumb {
      margin-top: 55px;
      .about-section__thumb-content {
        top: -55px;
        left: 25px;
        @include box(130px);
        padding: 10px;
        .h5 {
          font-size: 16px;
          span {
            font-size: 12px;
          }
        }
      }
    }
  }

  .choose-section {
    .choose-section__thumb-video {
      top: 50%;
      transform: translateY(-50%);
      width: 350px;
      height: 350px;
    }
  }

  .free {
    .thumb {
      &::before {
        transform: rotate(-14deg) translateX(24px) scaleY(1.5);
      }

      &::after {
        transform: rotate(-14deg) translateX(8px) scaleY(1.5);
      }
    }
  }

  .testimonial-two {
    .testimonial-two-pagination {
      top: 92%;
      right: 140px;
    }
  }

  .recent-project {
    .recent-project__slider-item-inner {
      .thumb {
        width: 450px;
        min-width: 450px;
      }

      .content {
        width: calc(100% - 450px);
      }
    }
  }

  .custom-quote {
    .quote-wrapper {
      height: 1100px;
    }
  }

  .portfolio {
    .img {
      background-size: 350px 100%;
    }
  }

  .quality-section {
    .rangu {
      height: auto !important;
      img {
        min-height: 340px;
        max-height: 400px;
      }
    }
  }
}

@media only screen and (max-width: 1199.98px) {
  // typography
  h1,
  .h1 {
    font-size: 48px;
    line-height: 66px;
  }

  // header
  .header {
    .nav__dropdown--alt {
      max-width: 100% !important;
      min-width: 100% !important;
      flex-direction: column;
      gap: 12px;
      ul {
        width: 100%;
        margin: 0px;

        &:nth-of-type(1) {
          margin-bottom: 12px;
        }
      }
    }

    .nav__menu-item--dropdown {
      .mega-menu {
        min-width: 100% !important;
        max-width: 100%;
        transform: translate(0px, 0px);
        padding: 40px 20px;
      }

      &:hover {
        .mega-menu {
          transform: translate(0px, 0px);
        }
      }
    }

    .mega-menu__inner {
      flex-direction: column;
      align-items: flex-start;
    }

    .mega-menu__single--alt {
      max-width: 400px;
      width: 100%;
    }

    .mega-menu__single-item {
      a {
        img {
          max-width: 70px;
        }

        span {
          font-size: 16px;
        }
      }
    }

    .mega-menu__single-head {
      margin-bottom: 20px;
      .h5 {
        font-size: 20px;
      }
    }
  }

  .header {
    .nav {
      padding: 17px 0px;
    }

    .nav__menu {
      position: fixed;
      top: 0px;
      left: 0px;
      bottom: 0px;
      min-width: 300px;
      max-width: 300px;
      height: 100vh;
      z-index: 99;
      overflow-x: clip;
      overflow-y: auto;
      padding: 40px 20px;
      background: $white;
      box-shadow: $shadow;
      transform: translateX(-110%);
      transition: 0.3s ease-in-out;

      &::-webkit-scrollbar {
        width: 0px;
      }
    }

    .nav__logo {
      column-gap: 30px;
    }

    .nav__menu-logo {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 30px;
      gap: 20px;

      img {
        max-width: 130px;
      }

      .nav__menu-close {
        font-size: 30px;
        color: $theme-color;
      }
    }

    .nav__menu-active {
      transform: translateX(0px);
    }

    .nav__menu-items {
      flex-direction: column;
      align-items: flex-start;
    }

    .nav__menu-item {
      width: 100%;
      margin-bottom: 12px;

      &:nth-last-of-type(1) {
        margin-bottom: 0px;
        margin-top: 0px;

        .btn {
          width: 100%;
          justify-content: center;
        }
      }
    }

    .nav__menu-item--dropdown {
      .nav__dropdown,
      .nav__dropdown-child {
        position: static;
        transform: translateY(0px);
        padding: 20px;
        margin-top: 5px;
        display: none;
        opacity: 1;
        visibility: visible;
        min-width: 100%;
        background-color: rgba(12, 169, 64, 0.4);
        box-shadow: $shadow;

        li {
          margin-bottom: 14px;
        }
      }

      .nav__dropdown-item {
        color: #3b3b3b !important;
      }

      .nav__dropdown-active {
        display: block !important;
        animation: atg 0.4s ease-in-out;
      }

      @keyframes atg {
        0% {
          transform: translateY(-10px);
        }

        100% {
          transform: translateY(0px);
        }
      }

      .nav__menu-link--dropdown {
        &:hover {
          color: $tertiary-color !important;

          &::after {
            transform: rotate(0deg);
            color: $tertiary-color !important;
          }
        }
      }

      .nav__menu-link--dropdown-active {
        color: $tertiary-color !important;

        &::after {
          transform: rotate(180deg) !important;
          color: $tertiary-color !important;
        }
      }

      .nav__menu-link-child {
        a {
          color: $theme-color !important;
          &:hover {
            color: $theme-color !important;
            &::after {
              color: $theme-color !important;
              transform: rotate(-90deg) !important;
            }
          }
          &::after {
            color: $theme-color !important;
            transform: rotate(-90deg) !important;
          }
        }
      }

      .nav__dropdown-child {
        left: 0px;
        top: 100%;
        right: 0px;
        width: 100%;
      }
    }

    .nav__menu-link {
      width: 100%;
      background-color: #f0efff;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16px;
      border-radius: 5px;
    }

    .backdrop {
      display: block;
    }

    .nav__uncollapsed {
      margin-left: 0px;
    }
  }

  .header-two {
    .nav {
      padding: 20px 0px;
    }
  }

  .choose-section {
    .video-wrap {
      right: 0px;
      left: unset;
    }
  }

  .pricing-section__inner {
    overflow-x: auto;
    &::-webkit-scrollbar {
      height: 4px;
    }

    &::-webkit-scrollbar-track {
      background-color: #cae6f7;
      border-radius: 5px;
    }

    &::-webkit-scrollbar-button,
    &::-webkit-scrollbar-thumb {
      background-color: $tertiary-color;
      border-radius: 5px;
    }
    .pricing-section__inner-item {
      min-width: 1200px;
    }
  }

  .sponsor {
    .sponsor__slider-item {
      img {
        max-width: 200px;
      }
    }

    .slick-current + .slick-active + .slick-active {
      img {
        opacity: 0.15;
      }
    }
    .slick-current + .slick-active {
      img {
        opacity: 1;
      }
    }
  }

  .counter {
    .counter__inner {
      flex-wrap: wrap;
      row-gap: 40px;
    }
    .counter__item {
      width: calc(50% - 12px);
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
    }
  }

  .service-two {
    .services__slider-single {
      .thumb {
        width: 150px;
        height: 150px;
      }
    }
  }

  .feature-two {
    .swiper-slide-active {
      .feature__item-inner {
        margin-right: unset;
      }
    }
  }

  .free {
    .thumb {
      width: calc(100% - 52%);
      &::before {
        transform: rotate(-10deg) translateX(10px) scaleY(1.5);
      }
      &::after {
        transform: rotate(-10deg) translateX(-7px) scaleY(1.5);
      }
    }
  }

  .news-two {
    .news-two__slider-item {
      .content {
        gap: 60px;
      }
    }

    .news-two__content {
      .news-two-pagination {
        top: 0px !important;
        margin-bottom: 0px !important;
      }
    }
  }

  .recent-project {
    .recent-project__inner {
      &::before {
        width: 50%;
        transform: rotate(50deg) scaleY(1.6) translateX(-300px)
          translateY(-106px);
      }
    }

    .paragraph {
      max-width: 600px;
    }

    .recent-project__slider-item-inner {
      flex-direction: column;
      .thumb {
        width: 100%;
        min-width: 100%;
      }

      .content {
        width: 100%;
      }
    }
  }

  .quote-overview {
    .quote-anime {
      top: 80%;
    }
  }

  .custom-quote {
    padding-bottom: 130px;
    .quote-wrapper {
      height: 1800px;
    }
  }

  .header {
    .rangu {
      img {
        min-height: 300px;
      }
    }
  }

  .portfolio {
    .img {
      background-size: 450px 100%;
    }
  }

  .footer-two__group-social {
    a {
      padding-left: 20px;
      padding-right: 20px;
    }
  }
}

@media only screen and (max-width: 991.98px) {
  .section {
    padding: 100px 0px;
  }

  .section__cta {
    margin-top: 50px;
  }

  .section__content-cta {
    margin-top: 40px;
  }

  .swiper-pagination-horizontal {
    justify-content: flex-start;
  }

  .section__content {
    .h6 {
      margin-bottom: 16px;
    }

    .h2 {
      margin-bottom: 20px;
    }

    .paragraph {
      p {
        margin-bottom: 20px;
        &:nth-last-of-type(1) {
          margin-bottom: 0px;
        }
      }
    }

    .cta__group {
      margin-top: 40px !important;
    }
  }

  // typography
  h1,
  .h1 {
    font-size: 30px;
    line-height: 40px;
  }

  h2,
  .h2 {
    font-size: 30px;
    line-height: 40px;
  }

  h3,
  .h3 {
    font-size: 24px;
    line-height: 34px;
  }

  h4,
  .h4 {
    font-size: 20px;
    line-height: 30px;
  }

  h5,
  .h5 {
    font-size: 18px;
    line-height: 28px;
  }

  // banner
  .banner {
    padding: 100px 0px;
  }

  .banner-two {
    padding: 180px 0px 100px;

    .banner__content {
      .h1 {
        span {
          &:nth-last-of-type(1) {
            margin-left: 80px;
          }
        }
      }
    }

    .banner-two__small-thumb {
      .one {
        top: 20%;
        right: 0%;
      }
    }
  }

  .banner-three {
    padding-top: 100px;
    .banner-three__content {
      text-align: center;

      .cta__group {
        margin-top: 25px !important;
      }
    }

    .banner-three__thumb {
      text-align: center;
      p {
        margin-left: auto;
        margin-right: auto;
      }
    }
  }

  //  service
  .services {
    .services__slider-single {
      height: 290px;
    }

    .services__slider-single__content {
      padding: 30px 20px;
      a {
        @include box(50px);
        font-size: 20px;
      }
    }
  }

  .about-section {
    .about-section__thumb {
      img {
        width: 100%;
      }
    }
    .paragraph {
      max-width: 100%;
    }
  }

  .work-section {
    .section__header {
      margin-bottom: 40px;
    }
  }

  .choose-section {
    .video-wrap {
      right: unset;
      left: 50%;
    }
  }

  .footer {
    .footer__head {
      margin-bottom: 24px;
    }
    .footer__nav {
      ul {
        justify-content: center;
      }
    }
    .footer__copy {
      margin-top: 24px;
    }
  }

  .testimonial-two {
    .testimonial-two-pagination {
      top: 96%;
      right: 140px;
    }
  }

  .news-two {
    .news-two__content {
      padding: 30px;
    }
    .news-two__slider-item {
      .content {
        gap: 30px;
        width: calc(100% - 300px);
      }

      .thumb {
        width: 300px;
      }
    }
  }

  .faq-two {
    .faq-two__thumb {
      text-align: start;
      margin: 0px;
      img {
        transform: translate(0px);
      }
    }
  }

  .custom-quote {
    padding-bottom: 100px;
  }

  .map-wrapper {
    max-height: 300px;

    iframe {
      width: 100%;
      height: 300px;
    }
  }

  .footer-two__group {
    padding: 40px 20px;
  }

  .footer-two {
    .gaper {
      row-gap: 30px;
    }
  }

  .footer-two__group-first {
    text-align: start;
  }

  .footer-two__group-second {
    gap: 30px;
    justify-content: start;
    flex-wrap: wrap;

    .single {
      text-align: start;
    }
  }

  .footer-two__group-social {
    .social {
      justify-content: flex-start;
      a {
        padding: 20px;
      }
    }
  }

  .custom-quote {
    .quote-wrapper {
      height: 1200px;
    }
  }

  .portfolio {
    .img {
      background-size: 330px 100% !important;
    }
  }
}

@media only screen and (max-width: 767.98px) {
  // typography
  h1,
  .h1 {
    font-size: 30px;
    line-height: 46px;
  }

  // banner
  .banner {
    .banner__content {
      .h6 {
        margin-bottom: 16px;
      }
      .cta__group {
        margin-top: 30px !important;
      }
    }
  }

  .banner-two {
    .banner__content {
      .h1 {
        span {
          &:nth-last-of-type(1) {
            margin-left: 30px;
          }
        }
      }
    }

    .cta__group {
      margin-top: 30px !important;
      justify-content: flex-start;
    }
  }

  .work-section {
    .work-section__inner {
      padding: 60px 30px;
    }
  }

  .sponsor {
    .sponsor__slider-item {
      img {
        max-width: 150px;
      }
    }
  }

  .counter {
    .counter__inner {
      padding: 60px 30px;
    }
    .h6 {
      font-size: 14px;
    }
  }

  .feature-two {
    .swiper-slide-active {
      .feature__item-inner {
        margin-left: unset;
        margin-right: unset;
      }
    }
  }

  .accordion {
    .accordion-button {
      font-size: 16px;
      padding: 30px 20px;
    }

    .accordion-body {
      padding: 0px 20px 30px;
    }
  }

  .project {
    .project__thumb {
      margin-bottom: 40px;
    }
    .primary-text {
      font-size: 16px;
    }
  }

  .news-two {
    .news-two__content {
      padding: 30px;
    }
    .news-two__slider-item {
      flex-direction: column;
      .content {
        width: 100%;
      }

      .thumb {
        width: 100%;
      }
    }

    .news-two-pagination {
      margin-top: 40px;
    }
  }

  .form-group-wrapper {
    flex-direction: column;
    .form-group-single {
      width: 100%;
    }
  }

  .video-modal-two {
    .video-btn {
      @include box(80px);
    }
  }

  .portfolio {
    .portfolio__filter-btns {
      gap: 16px;
      button {
        font-size: 14px;
      }
    }
  }

  .recent-project {
    .recent-project__inner {
      padding: 80px 30px;
      &::before {
        width: 70%;
        transform: rotate(50deg) scaleY(1.6) translateX(-250px)
          translateY(-106px);
      }
    }
  }

  .contact-main {
    .contact-main__content,
    .contact-main__thumb {
      padding: 60px 40px;
    }
  }

  .registration-inner {
    .single {
      padding: 60px 40px;
    }
  }

  .custom-quote {
    .quote-wrapper {
      height: 800px;
    }
  }

  .portfolio {
    .img {
      background-size: 510px 100% !important;
    }
  }

  .testimonial .testimonial__slider {
    min-width: 100%;
  }
}

@media only screen and (max-width: 576.98px) {
  // typography
  h1,
  .h1 {
    font-size: 24px;
    line-height: 34px;
  }

  h2,
  .h2 {
    font-size: 20px;
    line-height: 30px;
  }

  h3,
  .h3 {
    font-size: 20px;
    line-height: 30px;
  }

  h4,
  .h4 {
    font-size: 18px;
    line-height: 28px;
  }

  h6,
  .h6 {
    font-size: 14px;
  }

  .section__header {
    margin-bottom: 40px;
    .sub-title {
      margin-bottom: 12px;
    }
  }

  .section__header--alt {
    margin-bottom: 40px;
    .section__header {
      margin-bottom: 0px;
    }
  }

  .cta__group {
    margin-top: 40px;
  }

  .banner {
    .banner__small-thumb {
      .three {
        top: 40%;
        left: 10vw;
      }

      .one {
        top: 60%;
        right: 3vw;
      }
    }
  }

  .work-section {
    li {
      flex-direction: column;
      align-items: flex-start;
    }
  }

  .choose-section {
    .choose-section__thumb-video {
      width: 290px;
      height: 290px;
    }
  }

  .testimonial {
    .testimonial__slider-item {
      padding: 60px 20px;
      .quote {
        margin-bottom: 30px;
        i {
          font-size: 30px;
        }
      }

      .h4 {
        font-size: 15px;
      }

      hr {
        margin: 40px 0px;
      }

      .item__meta {
        flex-direction: column;
        align-items: flex-start;
      }
    }
  }

  .news-section {
    .content {
      padding: 36px 20px;
    }
  }

  .try-cta {
    .try-cta__inner {
      padding: 60px 20px;
    }
  }

  .counter {
    .counter__inner {
      padding: 60px 20px;
    }
    .counter__item {
      width: 100%;
    }
  }

  .services {
    .services__slider-single {
      width: calc(100% - 30px);
      margin-left: auto;
      margin-right: auto;
    }
  }

  .service-two {
    .services__slider-single {
      padding: 100px 20px;
    }
  }

  .feature-two {
    .feature__inner {
      padding: 40px 20px;
    }
  }

  .free {
    .free__inner {
      padding: 60px 30px;
    }
  }

  .testimonial-two {
    .testimonial-two-pagination {
      position: static;
      transform: translateY(0px);
      margin-top: 40px;
    }
  }

  .sponsor-three {
    .sponsor-three__inner {
      padding: 60px 20px;
    }
  }

  .quote-overview {
    .quote__counter {
      flex-direction: column;
      align-items: flex-start;
    }
  }

  .registration-inner {
    .single {
      padding: 60px 20px;
    }
  }

  .registration__content {
    .authentic {
      .btn {
        gap: 16px;
        padding: 18px 16px;
        font-size: 14px;
      }
    }
  }

  .blog-main {
    .blog-main__sidebar {
      padding: 30px 20px;
      .widget__list,
      .widget__tag,
      .widget__latest {
        a {
          font-size: 14px !important;
        }
      }
    }

    .blog-main__single {
      margin-bottom: 40px;
      .thumb,
      .content {
        padding: 30px 20px;
      }
    }
  }
  .blog-details {
    .bd-content {
      padding: 30px 20px;
    }

    .bd-quote {
      padding: 40px 20px;
    }
  }

  .custom-quote {
    .custom-quote__right {
      animation-direction: normal;
    }
    .quote-wrapper {
      height: 1200px;
    }
  }

  .quality-section,
  .service-de-thumb-alt {
    .rangu {
      .img {
        background-size: 100vw 100%;
      }
    }
  }

  .team-two {
    .team-two__slider-item {
      width: calc(100% - 30px);
      margin-left: auto;
      margin-right: auto;
    }
  }
}

@media only screen and (max-width: 424.98px) {
  .cta__group {
    flex-direction: column;
  }

  .btn {
    width: 100%;
  }

  .work-section {
    .work-section__inner {
      padding: 60px 20px;
    }
  }

  .choose-section {
    li {
      flex-direction: column;
      align-items: flex-start;
    }

    .cta__group {
      flex-direction: row;
    }
  }

  .banner-two {
    .cta__group {
      align-items: flex-start;
    }
  }

  .free {
    .free__inner {
      padding: 60px 20px;
    }

    .cta__group {
      justify-content: flex-start !important;
      align-items: flex-start !important;

      .btn {
        font-size: 12px;
        padding: 12px 16px;
      }
    }
  }

  .pricing-two {
    .pricing-two__single {
      padding: 30px 20px;
    }
  }

  .news-two {
    .news-two__content {
      padding: 30px 20px;
    }

    .news-two__alt {
      padding: 30px 20px;
    }
  }

  .pricing-main {
    .pricing-main__single {
      padding: 40px 20px;
      .anime {
        img {
          left: 20px;
          max-width: 40px;
        }
      }
    }
  }

  .recent-project {
    .recent-project__inner {
      padding: 80px 20px;
    }
  }

  .custom-quote {
    .trial__form {
      padding: 40px 20px;
    }
  }

  .contact-main {
    .contact-main__content,
    .contact-main__thumb {
      padding: 60px 20px;
    }
  }

  .blog-details {
    .blog-details__pagination {
      .latest-single {
        flex-direction: column;
        align-items: flex-start;
      }
    }
  }

  .thumb-radio {
    padding: 80px 20px;

    .radio {
      margin: 0px -20px;
    }
  }

  .portfolio {
    .img {
      background-size: 100vw 100% !important;
    }
  }
}

@media only screen and (max-width: 374.98px) {
  .sponsor {
    .sponsor__slider-item {
      img {
        max-width: 120px;
      }
    }
  }

  .footer-two__group-second {
    flex-direction: column;
    .single {
      width: 100%;
    }
  }
}

@media only screen and (min-width: 1200px) {
  .banner-three {
    .banner-three__content {
      .h1 {
        font-size: 48px;
      }
    }
  }
}

@media only screen and (min-width: 1400px) {
  .banner-three {
    .banner-three__content {
      .h1 {
        font-size: 64px;
        line-height: 74px;
      }
    }
  }
}

@media only screen and (min-width: 1440px) {
  .container {
    max-width: 1440px;
  }

  .choose-section {
    .choose-section__content {
      padding-left: 70px;
    }
  }
}



/* ====
 --------- (6.0) responsive styles end ---------
 ==== */
